<script setup lang="ts">
import { ref, watch } from 'vue';

defineOptions({
  name: 'DashboardRefresh'
});

interface Props {
  /** 是否正在刷新 */
  refreshing: boolean;
  /** 刷新类型 */
  type?: 'data' | 'chart' | 'system';
}

const props = withDefaults(defineProps<Props>(), {
  type: 'data'
});

const showEffect = ref(false);

watch(() => props.refreshing, (newVal) => {
  if (newVal) {
    showEffect.value = true;
  } else {
    setTimeout(() => {
      showEffect.value = false;
    }, 500);
  }
});

// 根据类型获取颜色
const getEffectColor = () => {
  const colorMap = {
    data: 'blue',
    chart: 'cyan',
    system: 'green'
  };
  return colorMap[props.type];
};
</script>

<template>
  <div class="relative">
    <!-- 刷新效果遮罩 -->
    <Transition
      name="refresh-effect"
      enter-active-class="transition-all duration-500 ease-out"
      leave-active-class="transition-all duration-300 ease-in"
      enter-from-class="opacity-0 scale-95"
      enter-to-class="opacity-100 scale-100"
      leave-from-class="opacity-100 scale-100"
      leave-to-class="opacity-0 scale-105"
    >
      <div
        v-if="showEffect"
        class="absolute inset-0 z-10 flex items-center justify-center rounded-8px backdrop-blur-sm"
        :class="{
          'bg-blue-500/10 border border-blue-400/30': props.type === 'data',
          'bg-cyan-500/10 border border-cyan-400/30': props.type === 'chart',
          'bg-green-500/10 border border-green-400/30': props.type === 'system'
        }"
      >
        <!-- 刷新指示器 -->
        <div class="text-center space-y-8px">
          <!-- 旋转图标 -->
          <div class="relative mx-auto w-32px h-32px">
            <div 
              class="absolute inset-0 border-2 border-transparent rounded-full animate-spin"
              :class="{
                'border-t-blue-400 border-r-blue-400/50': props.type === 'data',
                'border-t-cyan-400 border-r-cyan-400/50': props.type === 'chart',
                'border-t-green-400 border-r-green-400/50': props.type === 'system'
              }"
            />
            <div 
              class="absolute inset-2px border border-transparent rounded-full animate-[spin_1s_linear_infinite_reverse]"
              :class="{
                'border-b-blue-400/70': props.type === 'data',
                'border-b-cyan-400/70': props.type === 'chart',
                'border-b-green-400/70': props.type === 'system'
              }"
            />
            <!-- 中心点 -->
            <div 
              class="absolute inset-1/2 w-4px h-4px rounded-full transform -translate-x-1/2 -translate-y-1/2 animate-pulse"
              :class="{
                'bg-blue-400': props.type === 'data',
                'bg-cyan-400': props.type === 'chart',
                'bg-green-400': props.type === 'system'
              }"
            />
          </div>
          
          <!-- 刷新文本 -->
          <div 
            class="text-12px font-500 animate-pulse"
            :class="{
              'text-blue-300': props.type === 'data',
              'text-cyan-300': props.type === 'chart',
              'text-green-300': props.type === 'system'
            }"
          >
            {{ props.type === 'data' ? '数据更新中' : props.type === 'chart' ? '图表刷新中' : '系统同步中' }}
          </div>
        </div>

        <!-- 波纹效果 -->
        <div class="absolute inset-0 pointer-events-none">
          <div 
            class="absolute inset-1/2 w-4px h-4px rounded-full transform -translate-x-1/2 -translate-y-1/2 animate-[ripple_2s_ease-out_infinite]"
            :class="{
              'bg-blue-400/30': props.type === 'data',
              'bg-cyan-400/30': props.type === 'chart',
              'bg-green-400/30': props.type === 'system'
            }"
          />
          <div 
            class="absolute inset-1/2 w-4px h-4px rounded-full transform -translate-x-1/2 -translate-y-1/2 animate-[ripple_2s_ease-out_infinite]"
            :class="{
              'bg-blue-400/20': props.type === 'data',
              'bg-cyan-400/20': props.type === 'chart',
              'bg-green-400/20': props.type === 'system'
            }"
            style="animation-delay: 0.5s"
          />
        </div>
      </div>
    </Transition>

    <!-- 内容插槽 -->
    <div :class="{ 'animate-pulse': props.refreshing }">
      <slot />
    </div>
  </div>
</template>

<style scoped>
@keyframes ripple {
  0% {
    width: 4px;
    height: 4px;
    opacity: 1;
  }
  100% {
    width: 100px;
    height: 100px;
    opacity: 0;
  }
}

/* 数据更新闪烁效果 */
.refresh-effect-enter-active {
  animation: data-refresh 0.5s ease-out;
}

@keyframes data-refresh {
  0% { 
    background-color: rgba(59, 130, 246, 0);
    transform: scale(1);
  }
  50% { 
    background-color: rgba(59, 130, 246, 0.2);
    transform: scale(1.02);
  }
  100% { 
    background-color: rgba(59, 130, 246, 0);
    transform: scale(1);
  }
}
</style>
