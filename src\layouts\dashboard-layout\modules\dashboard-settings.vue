<script setup lang="ts">
import { ref, watch } from 'vue';
import { NButton, NForm, NFormItem, NIcon, NSwitch } from 'naive-ui';
import DashboardDialog from '@/components/dashboard/common/dashboard-dialog.vue';

defineOptions({
  name: 'DashboardSettings'
});

interface Props {
  visible: boolean;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'update:scale', value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 设置状态
const scaleEnabled = ref(true);

// 监听缩放设置变化
watch(scaleEnabled, newValue => {
  emit('update:scale', newValue);
});

const handleSave = () => {
  // 保存设置到本地存储
  localStorage.setItem('dashboard-scale-enabled', JSON.stringify(scaleEnabled.value));
  emit('update:visible', false);
};

const handleReset = () => {
  scaleEnabled.value = true;
  emit('update:scale', true);
};

// 初始化设置
const initSettings = () => {
  const savedScale = localStorage.getItem('dashboard-scale-enabled');
  if (savedScale) {
    scaleEnabled.value = JSON.parse(savedScale);
    emit('update:scale', scaleEnabled.value);
  }
};

// 组件挂载时初始化设置
initSettings();
</script>

<template>
  <DashboardDialog
    :visible="props.visible"
    title="大屏设置"
    title-icon="mdi:cog"
    width="480px"
    confirm-text="保存设置"
    @update:visible="emit('update:visible', $event)"
    @confirm="handleSave"
    @cancel="emit('update:visible', false)"
  >
    <NForm label-placement="left" label-width="auto" class="space-y-24px">
      <!-- 缩放控制 -->
      <NFormItem class="flex items-center justify-between">
        <template #label>
          <div class="flex items-center gap-8px">
            <NIcon size="16" class="text-blue-400">
              <SvgIcon icon="mdi:resize" />
            </NIcon>
            <span class="text-16px text-white font-500">自适应缩放</span>
          </div>
        </template>

        <div class="flex items-center gap-12px">
          <span class="text-14px text-gray-400">
            {{ scaleEnabled ? '已启用' : '已禁用' }}
          </span>
          <NSwitch v-model:value="scaleEnabled" size="medium" class="scale-switch" />
        </div>
      </NFormItem>

      <!-- 设置说明 -->
      <div class="border border-blue-400/20 rounded-12px bg-blue-500/10 p-16px">
        <div class="flex items-start gap-12px">
          <NIcon size="16" class="mt-2px text-blue-400">
            <SvgIcon icon="mdi:information-outline" />
          </NIcon>
          <div class="flex-1">
            <h4 class="mb-8px text-14px text-blue-300 font-500">功能说明</h4>
            <p class="text-13px text-gray-300 leading-relaxed">
              自适应缩放功能可以根据屏幕尺寸自动调整大屏内容的显示比例，确保在不同分辨率下都能获得最佳的视觉效果。
              关闭此功能后，大屏将以原始尺寸显示。
            </p>
          </div>
        </div>
      </div>
    </NForm>

    <template #footer>
      <div class="flex justify-end gap-12px">
        <NButton
          class="px-20px text-gray-400 transition-all duration-200 hover:bg-gray-700/50 hover:text-white"
          @click="handleReset"
        >
          <template #icon>
            <NIcon size="14">
              <SvgIcon icon="mdi:restore" />
            </NIcon>
          </template>
          重置
        </NButton>

        <NButton
          class="px-20px text-gray-400 transition-all duration-200 hover:bg-gray-700/50 hover:text-white"
          @click="emit('update:visible', false)"
        >
          取消
        </NButton>

        <NButton
          type="primary"
          class="bg-blue-600 px-24px text-white font-500 transition-all duration-200 hover:bg-blue-500"
          @click="handleSave"
        >
          <template #icon>
            <NIcon size="14">
              <SvgIcon icon="mdi:check" />
            </NIcon>
          </template>
          保存设置
        </NButton>
      </div>
    </template>
  </DashboardDialog>
</template>
