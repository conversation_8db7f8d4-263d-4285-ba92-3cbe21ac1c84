<script setup lang="ts">
import { onMounted, ref } from 'vue';

defineOptions({
  name: 'DashboardEntrance'
});

const isEntering = ref(true);
const showHeader = ref(false);
const showContent = ref(false);
const showFooter = ref(false);

onMounted(() => {
  // 分阶段显示各个部分
  setTimeout(() => {
    showHeader.value = true;
  }, 300);
  
  setTimeout(() => {
    showContent.value = true;
  }, 600);
  
  setTimeout(() => {
    showFooter.value = true;
  }, 900);
  
  setTimeout(() => {
    isEntering.value = false;
  }, 1200);
});
</script>

<template>
  <div class="relative h-full w-full">
    <!-- 入场动画遮罩 -->
    <Transition
      name="entrance-mask"
      enter-active-class="transition-all duration-1000 ease-out"
      leave-active-class="transition-all duration-800 ease-in"
      enter-from-class="opacity-100"
      enter-to-class="opacity-0"
      leave-from-class="opacity-0"
      leave-to-class="opacity-100"
    >
      <div
        v-if="isEntering"
        class="absolute inset-0 z-50 bg-gradient-to-br from-[#0a1628] via-[#082761] to-[#051a3e]"
      >
        <!-- 扫描线效果 -->
        <div class="absolute inset-0 overflow-hidden">
          <div class="absolute w-full h-2px bg-gradient-to-r from-transparent via-cyan-400 to-transparent animate-[scan_2s_ease-in-out_infinite] top-1/2" />
          <div class="absolute h-full w-2px bg-gradient-to-b from-transparent via-blue-400 to-transparent animate-[scan-vertical_2s_ease-in-out_infinite] left-1/2" />
        </div>
        
        <!-- 中央加载指示器 -->
        <div class="absolute inset-0 flex items-center justify-center">
          <div class="text-center space-y-16px">
            <div class="relative">
              <!-- 旋转环 -->
              <div class="w-60px h-60px border-2 border-blue-500/30 rounded-full animate-spin">
                <div class="absolute top-0 left-1/2 w-4px h-4px bg-cyan-400 rounded-full transform -translate-x-1/2 -translate-y-1/2" />
              </div>
              <!-- 内部脉冲 -->
              <div class="absolute inset-4px border border-blue-400/50 rounded-full animate-pulse" />
            </div>
            <div class="text-cyan-400 text-16px font-500 animate-pulse">系统启动中...</div>
          </div>
        </div>
      </div>
    </Transition>

    <!-- 主要内容 -->
    <div class="h-full w-full">
      <!-- 头部动画 -->
      <Transition
        name="slide-down"
        enter-active-class="transition-all duration-800 ease-out"
        enter-from-class="transform -translate-y-full opacity-0"
        enter-to-class="transform translate-y-0 opacity-100"
      >
        <div v-if="showHeader" class="header-container">
          <slot name="header" />
        </div>
      </Transition>

      <!-- 内容区域动画 -->
      <Transition
        name="fade-up"
        enter-active-class="transition-all duration-1000 ease-out"
        enter-from-class="transform translate-y-8 opacity-0 scale-95"
        enter-to-class="transform translate-y-0 opacity-100 scale-100"
      >
        <div v-if="showContent" class="content-container">
          <slot name="content" />
        </div>
      </Transition>

      <!-- 页脚动画 -->
      <Transition
        name="slide-up"
        enter-active-class="transition-all duration-800 ease-out"
        enter-from-class="transform translate-y-full opacity-0"
        enter-to-class="transform translate-y-0 opacity-100"
      >
        <div v-if="showFooter" class="footer-container">
          <slot name="footer" />
        </div>
      </Transition>
    </div>
  </div>
</template>

<style scoped>
@keyframes scan {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes scan-vertical {
  0% { transform: translateY(-100%); }
  100% { transform: translateY(100%); }
}

.header-container {
  animation: glow-pulse 3s ease-in-out infinite;
}

.content-container {
  animation: content-breathe 4s ease-in-out infinite;
}

.footer-container {
  animation: footer-glow 5s ease-in-out infinite;
}

@keyframes glow-pulse {
  0%, 100% { 
    filter: drop-shadow(0 0 5px rgba(59, 130, 246, 0.3));
  }
  50% { 
    filter: drop-shadow(0 0 20px rgba(59, 130, 246, 0.6));
  }
}

@keyframes content-breathe {
  0%, 100% { 
    transform: scale(1);
    filter: brightness(1);
  }
  50% { 
    transform: scale(1.002);
    filter: brightness(1.05);
  }
}

@keyframes footer-glow {
  0%, 100% { 
    box-shadow: 0 0 10px rgba(34, 197, 94, 0.2);
  }
  50% { 
    box-shadow: 0 0 30px rgba(34, 197, 94, 0.4);
  }
}
</style>
