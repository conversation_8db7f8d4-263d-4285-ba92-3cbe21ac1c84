<script setup lang="ts">
import DashboardDialog from '@/components/dashboard/common/dashboard-dialog.vue';

interface Props {
  visible: boolean;
  iframeUrl?: string;
}

const props = withDefaults(defineProps<Props>(), {
  iframeUrl: 'https://chat.openai.com'
});

const emit = defineEmits(['update:visible']);
</script>

<template>
  <DashboardDialog
    :visible="props.visible"
    title="AI助手"
    title-icon="mdi:web"
    width="480px"
    height="650px"
    :show-footer="false"
    :mask-closable="false"
    @update:visible="emit('update:visible', $event)"
  >
    <div class="h-[calc(650px-180px)]">
      <iframe
        :src="props.iframeUrl"
        class="h-full w-full border-0 rounded-8px"
        frameborder="0"
        allowfullscreen
        sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation"
      />
    </div>
  </DashboardDialog>
</template>
