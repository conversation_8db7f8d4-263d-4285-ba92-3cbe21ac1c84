<script setup lang="ts">
import { ref, watch } from 'vue';
import { NButton, NIcon, NModal } from 'naive-ui';

interface Props {
  visible: boolean;
  iframeUrl?: string;
}

const props = withDefaults(defineProps<Props>(), {
  iframeUrl: 'https://chat.openai.com'
});

const emit = defineEmits(['update:visible']);

const dialogVisible = ref(props.visible);

// 监听visible属性变化
watch(
  () => props.visible,
  val => {
    dialogVisible.value = val;
  }
);

// 监听内部visible变化，向父组件发送更新事件
watch(
  () => dialogVisible.value,
  val => {
    emit('update:visible', val);
  }
);

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false;
};
</script>

<template>
  <NModal v-model:show="dialogVisible" class="flex flex-col items-center justify-center" :mask-closable="false">
    <div
      class="h-[90vh] max-h-800px max-w-1200px w-[90vw] overflow-hidden border border-blue-400/30 rounded-16px bg-gray-900/95 shadow-[0_20px_60px_rgba(0,0,0,0.5)] backdrop-blur-20px"
    >
      <!-- 头部 -->
      <div
        class="flex items-center justify-between border-b border-blue-400/20 from-blue-600/20 to-blue-500/10 bg-gradient-to-r p-20px"
      >
        <div class="flex items-center gap-12px">
          <div
            class="h-40px w-40px flex items-center justify-center border border-blue-400/40 rounded-12px bg-blue-500/20"
          >
            <NIcon size="20" class="text-blue-300">
              <SvgIcon icon="mdi:web" />
            </NIcon>
          </div>
          <div>
            <h2 class="text-18px text-white font-600">AI助手</h2>
            <p class="text-12px text-blue-300">智能对话助手</p>
          </div>
        </div>

        <NButton
          quaternary
          circle
          class="text-gray-400 transition-all duration-200 hover:bg-gray-700/50 hover:text-white"
          @click="closeDialog"
        >
          <template #icon>
            <NIcon size="18">
              <SvgIcon icon="mdi:close" />
            </NIcon>
          </template>
        </NButton>
      </div>

      <!-- iframe内容区域 -->
      <div class="h-[calc(100%-80px)] flex-1">
        <iframe
          :src="iframeUrl"
          class="h-full w-full border-0"
          frameborder="0"
          allowfullscreen
          sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation"
        />
      </div>
    </div>
  </NModal>
</template>
