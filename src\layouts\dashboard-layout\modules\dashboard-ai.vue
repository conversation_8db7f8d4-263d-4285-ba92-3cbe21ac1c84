<script setup lang="ts">
import DashboardDialog from '@/components/dashboard/common/dashboard-dialog.vue';

interface Props {
  visible: boolean;
  iframeUrl?: string;
}

const props = withDefaults(defineProps<Props>(), {
  iframeUrl: 'https://chat.openai.com'
});

const emit = defineEmits(['update:visible']);
</script>

<template>
  <DashboardDialog
    :visible="props.visible"
    title="AI助手"
    title-icon="mdi:web"
    width="480px"
    height="650px"
    :show-footer="false"
    :mask-closable="false"
    :full-content="true"
    @update:visible="emit('update:visible', $event)"
  >
    <iframe
      :src="props.iframeUrl"
      class="h-full w-[480px] border-0"
      frameborder="0"
      allowfullscreen
      sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation"
    />
  </DashboardDialog>
</template>
