<script setup lang="ts">
import { ref } from 'vue';
import { NButton, NCard, NSpace } from 'naive-ui';
import DashboardRefresh from './dashboard-refresh.vue';

defineOptions({
  name: 'EffectsDemo'
});

const dataRefreshing = ref(false);
const chartRefreshing = ref(false);
const systemRefreshing = ref(false);

const triggerDataRefresh = () => {
  dataRefreshing.value = true;
  setTimeout(() => {
    dataRefreshing.value = false;
  }, 2000);
};

const triggerChartRefresh = () => {
  chartRefreshing.value = true;
  setTimeout(() => {
    chartRefreshing.value = false;
  }, 2000);
};

const triggerSystemRefresh = () => {
  systemRefreshing.value = true;
  setTimeout(() => {
    systemRefreshing.value = false;
  }, 2000);
};
</script>

<template>
  <div class="p-24px space-y-24px">
    <h2 class="text-24px text-white font-600 mb-24px">大屏动态效果演示</h2>
    
    <!-- 控制按钮 -->
    <NSpace>
      <NButton type="primary" @click="triggerDataRefresh">
        触发数据刷新动画
      </NButton>
      <NButton type="info" @click="triggerChartRefresh">
        触发图表刷新动画
      </NButton>
      <NButton type="success" @click="triggerSystemRefresh">
        触发系统刷新动画
      </NButton>
    </NSpace>

    <!-- 演示卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-24px">
      <!-- 数据卡片 -->
      <DashboardRefresh :refreshing="dataRefreshing" type="data">
        <NCard class="bg-gray-800/50 border border-blue-400/30 backdrop-blur-sm">
          <template #header>
            <div class="flex items-center gap-8px text-blue-300">
              <div class="w-8px h-8px bg-blue-400 rounded-full animate-pulse" />
              <span>实时数据</span>
            </div>
          </template>
          
          <div class="space-y-16px">
            <div class="text-center">
              <div class="text-32px text-blue-400 font-bold">1,234</div>
              <div class="text-12px text-gray-400">在线用户</div>
            </div>
            <div class="text-center">
              <div class="text-24px text-green-400 font-bold">98.5%</div>
              <div class="text-12px text-gray-400">系统可用性</div>
            </div>
          </div>
        </NCard>
      </DashboardRefresh>

      <!-- 图表卡片 -->
      <DashboardRefresh :refreshing="chartRefreshing" type="chart">
        <NCard class="bg-gray-800/50 border border-cyan-400/30 backdrop-blur-sm">
          <template #header>
            <div class="flex items-center gap-8px text-cyan-300">
              <div class="w-8px h-8px bg-cyan-400 rounded-full animate-pulse" />
              <span>数据图表</span>
            </div>
          </template>
          
          <div class="h-120px flex items-center justify-center">
            <!-- 模拟图表 -->
            <div class="flex items-end gap-4px">
              <div v-for="i in 8" :key="i" 
                   class="w-8px bg-gradient-to-t from-cyan-600 to-cyan-400 rounded-t-sm animate-[bar-grow_2s_ease-in-out_infinite]"
                   :style="{ 
                     height: `${20 + Math.random() * 60}px`,
                     animationDelay: `${i * 0.1}s`
                   }"
              />
            </div>
          </div>
        </NCard>
      </DashboardRefresh>

      <!-- 系统状态卡片 -->
      <DashboardRefresh :refreshing="systemRefreshing" type="system">
        <NCard class="bg-gray-800/50 border border-green-400/30 backdrop-blur-sm">
          <template #header>
            <div class="flex items-center gap-8px text-green-300">
              <div class="w-8px h-8px bg-green-400 rounded-full animate-pulse" />
              <span>系统状态</span>
            </div>
          </template>
          
          <div class="space-y-12px">
            <div class="flex justify-between items-center">
              <span class="text-gray-300">CPU使用率</span>
              <div class="flex items-center gap-8px">
                <div class="w-60px h-4px bg-gray-700 rounded-full overflow-hidden">
                  <div class="h-full bg-gradient-to-r from-green-500 to-green-400 rounded-full animate-[progress_3s_ease-in-out_infinite]" style="width: 65%" />
                </div>
                <span class="text-green-400 text-12px">65%</span>
              </div>
            </div>
            
            <div class="flex justify-between items-center">
              <span class="text-gray-300">内存使用率</span>
              <div class="flex items-center gap-8px">
                <div class="w-60px h-4px bg-gray-700 rounded-full overflow-hidden">
                  <div class="h-full bg-gradient-to-r from-blue-500 to-blue-400 rounded-full animate-[progress_3s_ease-in-out_infinite]" style="width: 45%; animation-delay: 0.5s" />
                </div>
                <span class="text-blue-400 text-12px">45%</span>
              </div>
            </div>
            
            <div class="flex justify-between items-center">
              <span class="text-gray-300">网络状态</span>
              <div class="flex items-center gap-4px">
                <div class="w-3px h-3px bg-green-400 rounded-full animate-ping" />
                <span class="text-green-400 text-12px">正常</span>
              </div>
            </div>
          </div>
        </NCard>
      </DashboardRefresh>
    </div>

    <!-- 使用说明 -->
    <NCard class="bg-gray-800/30 border border-gray-600/30">
      <template #header>
        <span class="text-white font-500">动态效果说明</span>
      </template>
      
      <div class="space-y-12px text-gray-300 text-14px">
        <p><strong class="text-blue-400">页面加载：</strong>包含Logo动画、进度条、打字机效果和粒子背景</p>
        <p><strong class="text-cyan-400">页面入场：</strong>分阶段显示头部、内容、页脚，带有扫描线和缩放效果</p>
        <p><strong class="text-green-400">背景动画：</strong>网格移动、粒子浮动、光束扫描、边框发光</p>
        <p><strong class="text-yellow-400">交互动画：</strong>卡片悬停、按钮点击、数据刷新指示器</p>
        <p><strong class="text-purple-400">数据更新：</strong>支持不同类型的刷新动画（数据/图表/系统）</p>
      </div>
    </NCard>
  </div>
</template>

<style scoped>
@keyframes bar-grow {
  0%, 100% { 
    transform: scaleY(0.8);
    opacity: 0.7;
  }
  50% { 
    transform: scaleY(1.2);
    opacity: 1;
  }
}

@keyframes progress {
  0%, 100% { 
    transform: scaleX(1);
  }
  50% { 
    transform: scaleX(1.1);
  }
}

/* 卡片悬停效果 */
:deep(.n-card) {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

:deep(.n-card:hover) {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}
</style>
