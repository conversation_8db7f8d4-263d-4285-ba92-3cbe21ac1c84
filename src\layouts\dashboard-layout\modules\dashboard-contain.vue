<script setup lang="ts">
import { onBeforeUnmount, onMounted, ref, watch } from 'vue';

defineOptions({
  name: 'DashboardContain'
});

interface Props {
  /** 设计宽度 */
  designWidth?: number;
  /** 设计高度 */
  designHeight?: number;
  /** 是否启用缩放 */
  enableScale?: boolean;
}

// 组件属性默认值
const props = withDefaults(defineProps<Props>(), {
  designWidth: 1920,
  designHeight: 1080,
  enableScale: true
});

// 大屏容器引用
const containerRef = ref<HTMLElement | null>(null);

/**
 * 计算大屏缩放比例
 * @returns {number} 缩放比例
 */
const getScale = () => {
  const ww = window.innerWidth / props.designWidth;
  const wh = window.innerHeight / props.designHeight;
  // 限制最小缩放比例，确保在小屏幕上不会过度缩小
  return ww < wh ? ww : wh;
};

// 防抖定时器和动画帧ID
const resizeTimer: number | null = null;
let animationFrameId: number | null = null;

/**
 * 响应窗口大小变化，重新计算缩放比例
 */
const resize = () => {
  if (!containerRef.value) return;

  // 取消之前的动画帧和定时器
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId);
  }
  if (resizeTimer) {
    clearTimeout(resizeTimer);
  }

  // 使用 requestAnimationFrame 确保在下一帧执行
  animationFrameId = requestAnimationFrame(() => {
    if (!containerRef.value) return;

    const element = containerRef.value;

    if (props.enableScale) {
      // 缩放模式：固定尺寸，居中缩放
      const scale = getScale();
      element.style.cssText = `
        transform: scale(${scale}) translate(-50%, -50%);
        transform-origin: top left;
        overflow: hidden;
        width: ${props.designWidth}px;
        height: ${props.designHeight}px;
        position: fixed;
        left: 50%;
        top: 50%;
        transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      `;
    } else {
      // 非缩放模式：全屏显示，主容器不滚动
      element.style.cssText = `
        transform: none;
        transform-origin: top left;
        overflow: hidden;
        width: 100%;
        height: 100%;
        position: relative;
        left: 0;
        top: 0;
        transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      `;
    }
  });
};

/**
 * 初始化大屏容器尺寸和缩放
 */
onMounted(() => {
  // 初始化时直接调用 resize 方法
  resize();
  window.addEventListener('resize', resize);
});

/**
 * 监听enableScale属性变化
 */
watch(
  () => props.enableScale,
  () => {
    resize();
  }
);

/**
 * 组件销毁前移除事件监听
 */
onBeforeUnmount(() => {
  window.removeEventListener('resize', resize);
  if (resizeTimer) {
    clearTimeout(resizeTimer);
  }
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId);
  }
});
</script>

<template>
  <div class="h-full w-full overflow-hidden bg-[#082761] relative">
    <!-- 动态背景效果 -->
    <div class="absolute inset-0 overflow-hidden">
      <!-- 网格背景 -->
      <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmZmZmYiIGZpbGwtb3BhY2l0eT0iMC4zIj48cGF0aCBkPSJNNDAgNDBWMGgtMXY0MGgxek0wIDQwVjBoMXY0MEgweiIvPjwvZz48L2c+PC9zdmc+')] animate-[grid-move_20s_linear_infinite]" />
      </div>

      <!-- 粒子效果 -->
      <div class="absolute inset-0 pointer-events-none">
        <div v-for="i in 15" :key="i"
             class="absolute w-1px h-1px bg-blue-400/40 rounded-full animate-[particle-float_6s_ease-in-out_infinite]"
             :style="{
               left: `${Math.random() * 100}%`,
               top: `${Math.random() * 100}%`,
               animationDelay: `${Math.random() * 6}s`
             }"
        />
      </div>

      <!-- 光束效果 -->
      <div class="absolute inset-0 pointer-events-none">
        <div class="absolute top-0 left-1/4 w-px h-full bg-gradient-to-b from-transparent via-blue-400/20 to-transparent animate-[beam_8s_ease-in-out_infinite]" />
        <div class="absolute top-0 right-1/3 w-px h-full bg-gradient-to-b from-transparent via-cyan-400/20 to-transparent animate-[beam_8s_ease-in-out_infinite]" style="animation-delay: 2s" />
        <div class="absolute left-0 top-1/3 w-full h-px bg-gradient-to-r from-transparent via-blue-400/20 to-transparent animate-[beam-horizontal_10s_ease-in-out_infinite]" />
      </div>

      <!-- 扫描线效果 -->
      <div class="absolute inset-0 pointer-events-none overflow-hidden">
        <div class="absolute w-full h-0.5 bg-gradient-to-r from-transparent via-cyan-400/50 to-transparent animate-[scan-line_4s_ease-in-out_infinite] top-1/2" />
      </div>
    </div>

    <!-- 主容器 -->
    <div
      ref="containerRef"
      class="relative z-999 flex flex-col origin-top-left transform-gpu transition-all duration-600 ease-out will-change-transform"
    >
      <slot />
    </div>
  </div>
</template>

<style scoped>
@keyframes grid-move {
  0% { transform: translate(0, 0); }
  100% { transform: translate(40px, 40px); }
}

@keyframes particle-float {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0;
  }
  10% { opacity: 1; }
  90% { opacity: 1; }
  50% {
    transform: translateY(-30px) translateX(10px);
    opacity: 0.8;
  }
}

@keyframes beam {
  0%, 100% {
    opacity: 0;
    transform: scaleY(0);
  }
  50% {
    opacity: 1;
    transform: scaleY(1);
  }
}

@keyframes beam-horizontal {
  0%, 100% {
    opacity: 0;
    transform: scaleX(0);
  }
  50% {
    opacity: 1;
    transform: scaleX(1);
  }
}

@keyframes scan-line {
  0% {
    transform: translateY(-100vh);
    opacity: 0;
  }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% {
    transform: translateY(100vh);
    opacity: 0;
  }
}
</style>
