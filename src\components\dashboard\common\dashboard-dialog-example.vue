<script setup lang="ts">
import { ref } from 'vue';
import { NButton, NForm, NFormItem, NInput, NSelect, NSpace } from 'naive-ui';
import DashboardDialog from './dashboard-dialog.vue';

defineOptions({
  name: 'DashboardDialogExample'
});

// 对话框显示状态
const basicDialogVisible = ref(false);
const confirmDialogVisible = ref(false);
const customDialogVisible = ref(false);
const iframeDialogVisible = ref(false);
const formDialogVisible = ref(false);

// 表单数据
const formData = ref({
  name: '',
  type: '',
  description: ''
});

const typeOptions = [
  { label: '信息', value: 'info' },
  { label: '成功', value: 'success' },
  { label: '警告', value: 'warning' },
  { label: '错误', value: 'error' }
];

// 处理表单提交
const handleFormSubmit = () => {
  console.log('表单数据:', formData.value);
  formDialogVisible.value = false;
};

// 处理确认对话框
const handleConfirm = () => {
  console.log('用户点击了确认');
  confirmDialogVisible.value = false;
};
</script>

<template>
  <div class="p-24px space-y-16px">
    <h2 class="text-24px text-white font-600 mb-24px">通用对话框组件示例</h2>
    
    <NSpace>
      <!-- 基础对话框 -->
      <NButton type="primary" @click="basicDialogVisible = true">
        基础对话框
      </NButton>

      <!-- 确认对话框 -->
      <NButton type="warning" @click="confirmDialogVisible = true">
        确认对话框
      </NButton>

      <!-- 自定义对话框 -->
      <NButton type="success" @click="customDialogVisible = true">
        自定义对话框
      </NButton>

      <!-- 表单对话框 -->
      <NButton type="info" @click="formDialogVisible = true">
        表单对话框
      </NButton>

      <!-- iframe对话框 -->
      <NButton @click="iframeDialogVisible = true">
        iframe对话框
      </NButton>
    </NSpace>

    <!-- 基础对话框 -->
    <DashboardDialog
      v-model:visible="basicDialogVisible"
      title="基础对话框"
      title-icon="mdi:information-outline"
    >
      <p class="text-gray-300 leading-relaxed">
        这是一个基础的对话框示例，包含标题、内容和操作按钮。
        你可以通过props来自定义对话框的外观和行为。
      </p>
    </DashboardDialog>

    <!-- 确认对话框 -->
    <DashboardDialog
      v-model:visible="confirmDialogVisible"
      title="确认操作"
      title-icon="mdi:alert-circle-outline"
      type="warning"
      confirm-text="确认删除"
      cancel-text="取消"
      @confirm="handleConfirm"
    >
      <p class="text-gray-300 leading-relaxed">
        您确定要删除这个项目吗？此操作不可撤销。
      </p>
    </DashboardDialog>

    <!-- 自定义对话框 -->
    <DashboardDialog
      v-model:visible="customDialogVisible"
      title="自定义对话框"
      title-icon="mdi:palette-outline"
      type="success"
      width="600px"
      :show-cancel="false"
      confirm-text="知道了"
    >
      <div class="space-y-16px">
        <div class="border border-green-400/20 rounded-12px bg-green-500/10 p-16px">
          <h4 class="text-green-300 font-500 mb-8px">成功提示</h4>
          <p class="text-gray-300 text-14px leading-relaxed">
            操作已成功完成！这是一个自定义样式的对话框，展示了不同的颜色主题。
          </p>
        </div>
        
        <div class="grid grid-cols-2 gap-16px">
          <div class="text-center p-16px border border-gray-600/30 rounded-8px">
            <div class="text-24px text-blue-400 font-600">1,234</div>
            <div class="text-12px text-gray-400">总数量</div>
          </div>
          <div class="text-center p-16px border border-gray-600/30 rounded-8px">
            <div class="text-24px text-green-400 font-600">98.5%</div>
            <div class="text-12px text-gray-400">成功率</div>
          </div>
        </div>
      </div>
    </DashboardDialog>

    <!-- 表单对话框 -->
    <DashboardDialog
      v-model:visible="formDialogVisible"
      title="添加项目"
      title-icon="mdi:plus-circle-outline"
      width="500px"
      confirm-text="保存"
      @confirm="handleFormSubmit"
    >
      <NForm :model="formData" label-placement="left" label-width="80px" class="space-y-16px">
        <NFormItem label="项目名称" required>
          <NInput v-model:value="formData.name" placeholder="请输入项目名称" />
        </NFormItem>
        
        <NFormItem label="项目类型" required>
          <NSelect 
            v-model:value="formData.type" 
            :options="typeOptions" 
            placeholder="请选择项目类型" 
          />
        </NFormItem>
        
        <NFormItem label="项目描述">
          <NInput 
            v-model:value="formData.description" 
            type="textarea" 
            placeholder="请输入项目描述"
            :rows="3"
          />
        </NFormItem>
      </NForm>
    </DashboardDialog>

    <!-- iframe对话框 -->
    <DashboardDialog
      v-model:visible="iframeDialogVisible"
      title="嵌入网页"
      title-icon="mdi:web"
      width="90vw"
      height="90vh"
      :show-footer="false"
    >
      <div class="h-[calc(90vh-120px)]">
        <iframe
          src="https://www.baidu.com"
          class="w-full h-full border-0 rounded-8px"
          frameborder="0"
          allowfullscreen
        />
      </div>
    </DashboardDialog>
  </div>
</template>

<style scoped>
/* 自定义样式 */
.scale-switch {
  --n-rail-color-active: #3b82f6;
}
</style>
