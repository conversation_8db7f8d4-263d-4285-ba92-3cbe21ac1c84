<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { NConfigProvider } from 'naive-ui';
import { dashboardTheme } from '@/theme/dashboard-theme';
import GlobalContent from '../modules/global-content/index.vue';
import DashboardHeader from './modules/dashboard-header.vue';
import DashboardFooter from './modules/dashboard-footer.vue';
import DashboardContain from './modules/dashboard-contain.vue';
import DashboardLoading from '@/components/dashboard/effects/dashboard-loading.vue';
import DashboardEntrance from '@/components/dashboard/effects/dashboard-entrance.vue';

defineOptions({
  name: 'DashboardLayout'
});

const isLoading = ref(true);
const isEntering = ref(false);
const enableScale = ref(true);

// 处理缩放设置变更
const handleScaleChange = (value: boolean) => {
  enableScale.value = value;
};

onMounted(() => {
  // 加载阶段
  setTimeout(() => {
    isLoading.value = false;
    isEntering.value = true;
  }, 2500);

  // 入场动画阶段
  setTimeout(() => {
    isEntering.value = false;
  }, 4000);
});
</script>

<template>
  <NConfigProvider :theme-overrides="dashboardTheme" class="h-full">
    <!-- 加载动画 -->
    <DashboardLoading :visible="isLoading" />

    <!-- 主要内容 -->
    <div class="relative h-full w-full overflow-hidden" :class="{ 'no-scale': !enableScale }">
      <DashboardContain :enable-scale="enableScale">
        <DashboardEntrance :class="{ 'animate-entrance': isEntering }">
          <template #header>
            <!-- 顶部标题栏和导航栏 -->
            <DashboardHeader title="智能数据大屏" @update:scale="handleScaleChange" />
          </template>

          <template #content>
            <!-- 内容区域 -->
            <div
              class="relative z-1000 min-h-0 flex-1 bg-[url('@/assets/dashboard/imgs/dashboard-background.jpg')] bg-cover bg-center"
              :class="{ 'overflow-hidden': enableScale, 'overflow-auto dashboard-scrollbar': !enableScale }"
            >
              <div class="h-full pl-20px pr-20px">
                <GlobalContent :show-padding="false" />
              </div>
            </div>
          </template>

          <template #footer>
            <!-- 页脚 -->
            <DashboardFooter />
          </template>
        </DashboardEntrance>
      </DashboardContain>
    </div>
  </NConfigProvider>
</template>

<style scoped>
.bg-layout {
  background-color: transparent;
}

/* 入场动画效果 */
.animate-entrance {
  animation: dashboard-entrance 1.5s ease-out;
}

@keyframes dashboard-entrance {
  0% {
    opacity: 0;
    transform: scale(0.95) translateY(20px);
    filter: blur(10px);
  }
  50% {
    opacity: 0.8;
    transform: scale(0.98) translateY(10px);
    filter: blur(5px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
    filter: blur(0);
  }
}

/* 全局动画增强 */
:deep(.dashboard-card) {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

:deep(.dashboard-card:hover) {
  transform: translateY(-2px);
  box-shadow: 0 10px 40px rgba(59, 130, 246, 0.3);
}

/* 数据更新动画 */
@keyframes data-update {
  0% {
    background-color: rgba(59, 130, 246, 0.1);
    transform: scale(1);
  }
  50% {
    background-color: rgba(59, 130, 246, 0.3);
    transform: scale(1.02);
  }
  100% {
    background-color: rgba(59, 130, 246, 0.1);
    transform: scale(1);
  }
}

.data-update {
  animation: data-update 0.6s ease-in-out;
}
</style>
