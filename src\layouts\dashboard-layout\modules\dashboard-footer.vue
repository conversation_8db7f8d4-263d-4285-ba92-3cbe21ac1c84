<script setup lang="ts">
import { computed } from 'vue';

defineOptions({
  name: 'DashboardFooter'
});

// 获取当前年份
const currentYear = computed(() => new Date().getFullYear());
</script>

<template>
  <footer
    class="relative h-28px w-full bg-[length:100%_100%] bg-[url('@/assets/dashboard/imgs/dashboard-footer.png')] bg-center bg-no-repeat overflow-hidden"
  >
    <!-- 动态背景效果 -->
    <div class="absolute inset-0 pointer-events-none">
      <div class="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-green-400/50 to-transparent animate-[footer-line_5s_ease-in-out_infinite]" />
      <div class="absolute inset-0 bg-gradient-to-r from-transparent via-green-500/5 to-transparent animate-[footer-glow_6s_ease-in-out_infinite]" />
    </div>

    <div class="relative h-full flex items-center justify-center">
      <span class="text-center text-sm text-white/70 animate-[footer-text_4s_ease-in-out_infinite]">
        © {{ currentYear }} 长沙移动网络中台
      </span>
    </div>
  </footer>
</template>

<style scoped>
@keyframes footer-line {
  0%, 100% {
    transform: scaleX(0);
    opacity: 0;
  }
  50% {
    transform: scaleX(1);
    opacity: 1;
  }
}

@keyframes footer-glow {
  0%, 100% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(100%);
  }
}

@keyframes footer-text {
  0%, 100% {
    opacity: 0.7;
  }
  50% {
    opacity: 0.9;
    text-shadow: 0 0 10px rgba(34, 197, 94, 0.3);
  }
}
</style>
