<script setup lang="ts">
import { ref } from 'vue';
import { NButton, NSpace } from 'naive-ui';
import DashboardDialog from './dashboard-dialog.vue';

defineOptions({
  name: 'DialogTest'
});

const normalDialogVisible = ref(false);
const iframeDialogVisible = ref(false);
</script>

<template>
  <div class="p-24px space-y-16px">
    <h2 class="text-24px text-white font-600 mb-24px">对话框布局测试</h2>
    
    <NSpace>
      <NButton type="primary" @click="normalDialogVisible = true">
        普通对话框
      </NButton>
      
      <NButton type="success" @click="iframeDialogVisible = true">
        iframe对话框
      </NButton>
    </NSpace>

    <!-- 普通对话框 -->
    <DashboardDialog
      v-model:visible="normalDialogVisible"
      title="普通对话框"
      title-icon="mdi:information-outline"
      width="480px"
    >
      <p class="text-gray-300 leading-relaxed mb-16px">
        这是一个普通的对话框，标题在左边，关闭按钮在右边。
      </p>
      <p class="text-gray-300 leading-relaxed">
        内容区域有正常的padding，适合显示文本和表单等内容。
      </p>
    </DashboardDialog>

    <!-- iframe对话框 -->
    <DashboardDialog
      v-model:visible="iframeDialogVisible"
      title="iframe对话框"
      title-icon="mdi:web"
      width="800px"
      height="600px"
      :show-footer="false"
      :full-content="true"
    >
      <div class="h-[calc(600px-80px)]">
        <iframe
          src="https://www.baidu.com"
          class="h-full w-full border-0"
          frameborder="0"
          allowfullscreen
        />
      </div>
    </DashboardDialog>
  </div>
</template>
