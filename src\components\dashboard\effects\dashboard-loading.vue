<script setup lang="ts">
import { onMounted, ref } from 'vue';

defineOptions({
  name: 'DashboardLoading'
});

interface Props {
  /** 是否显示加载动画 */
  visible: boolean;
  /** 加载文本 */
  text?: string;
}

const props = withDefaults(defineProps<Props>(), {
  text: '智能数据大屏系统'
});

const progress = ref(0);
const currentText = ref('');
const showContent = ref(false);

// 模拟加载进度
const simulateProgress = () => {
  const interval = setInterval(() => {
    progress.value += Math.random() * 15;
    if (progress.value >= 100) {
      progress.value = 100;
      clearInterval(interval);
      setTimeout(() => {
        showContent.value = true;
      }, 500);
    }
  }, 100);
};

// 打字机效果
const typewriterEffect = () => {
  const text = props.text;
  let index = 0;
  const interval = setInterval(() => {
    currentText.value = text.slice(0, index);
    index++;
    if (index > text.length) {
      clearInterval(interval);
    }
  }, 100);
};

onMounted(() => {
  if (props.visible) {
    typewriterEffect();
    simulateProgress();
  }
});
</script>

<template>
  <Transition
    name="loading"
    enter-active-class="transition-all duration-1000 ease-out"
    leave-active-class="transition-all duration-800 ease-in"
    enter-from-class="opacity-0"
    enter-to-class="opacity-100"
    leave-from-class="opacity-100"
    leave-to-class="opacity-0"
  >
    <div
      v-if="visible"
      class="fixed inset-0 z-9999 flex flex-col items-center justify-center bg-gradient-to-br from-[#0a1628] via-[#082761] to-[#051a3e]"
    >
      <!-- 背景动画网格 -->
      <div class="absolute inset-0 opacity-20">
        <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="1"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] animate-pulse" />
        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-blue-500/5 to-transparent animate-[shimmer_3s_ease-in-out_infinite]" />
      </div>

      <!-- 中央加载区域 -->
      <div class="relative z-10 flex flex-col items-center space-y-32px">
        <!-- Logo和标题 -->
        <div class="text-center space-y-16px">
          <div class="relative">
            <!-- 发光效果 -->
            <div class="absolute inset-0 blur-xl bg-gradient-to-r from-blue-400 to-cyan-400 opacity-30 animate-pulse" />
            <!-- 主Logo -->
            <div class="relative h-80px w-80px mx-auto flex items-center justify-center border-2 border-blue-400/50 rounded-20px bg-gradient-to-br from-blue-500/20 to-cyan-500/20 backdrop-blur-sm">
              <div class="text-40px text-blue-300 animate-[spin_4s_linear_infinite]">
                ⚡
              </div>
            </div>
          </div>
          
          <!-- 标题文字 -->
          <h1 class="text-32px text-white font-bold tracking-wider">
            <span class="bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">
              {{ currentText }}
            </span>
            <span class="animate-blink text-blue-400">|</span>
          </h1>
        </div>

        <!-- 进度条 -->
        <div class="w-300px space-y-12px">
          <div class="flex justify-between text-14px text-blue-300">
            <span>系统初始化中...</span>
            <span>{{ Math.round(progress) }}%</span>
          </div>
          
          <!-- 进度条容器 -->
          <div class="relative h-4px w-full overflow-hidden rounded-full bg-gray-800/50 border border-blue-500/30">
            <!-- 背景光效 -->
            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-blue-500/20 to-transparent animate-[slide_2s_ease-in-out_infinite]" />
            
            <!-- 进度条 -->
            <div 
              class="h-full bg-gradient-to-r from-blue-500 via-cyan-400 to-blue-500 transition-all duration-300 ease-out relative overflow-hidden"
              :style="{ width: `${progress}%` }"
            >
              <!-- 进度条光效 -->
              <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-[shimmer_1.5s_ease-in-out_infinite]" />
            </div>
          </div>
        </div>

        <!-- 加载状态指示器 -->
        <div class="flex items-center space-x-8px text-blue-300">
          <div class="flex space-x-2px">
            <div class="h-8px w-8px rounded-full bg-blue-400 animate-bounce" style="animation-delay: 0ms" />
            <div class="h-8px w-8px rounded-full bg-blue-400 animate-bounce" style="animation-delay: 150ms" />
            <div class="h-8px w-8px rounded-full bg-blue-400 animate-bounce" style="animation-delay: 300ms" />
          </div>
          <span class="text-14px">正在加载数据大屏组件</span>
        </div>

        <!-- 版本信息 -->
        <div class="absolute bottom-40px text-center text-12px text-gray-400 space-y-4px">
          <div>Powered by Vue 3 + TypeScript</div>
          <div class="text-blue-400">智能数据大屏系统 v2.0</div>
        </div>
      </div>

      <!-- 粒子效果 -->
      <div class="absolute inset-0 overflow-hidden pointer-events-none">
        <div v-for="i in 20" :key="i" 
             class="absolute w-2px h-2px bg-blue-400/30 rounded-full animate-float"
             :style="{
               left: `${Math.random() * 100}%`,
               top: `${Math.random() * 100}%`,
               animationDelay: `${Math.random() * 3}s`,
               animationDuration: `${3 + Math.random() * 2}s`
             }"
        />
      </div>
    </div>
  </Transition>
</template>

<style scoped>
@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes slide {
  0%, 100% { transform: translateX(-100%); }
  50% { transform: translateX(100%); }
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

@keyframes float {
  0%, 100% { 
    transform: translateY(0px) rotate(0deg);
    opacity: 0;
  }
  10% { opacity: 1; }
  90% { opacity: 1; }
  50% { 
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.8;
  }
}

.animate-blink {
  animation: blink 1s infinite;
}

.animate-float {
  animation: float 4s infinite linear;
}
</style>
