<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useFullscreen } from '@vueuse/core';
import DashboardAi from './dashboard-ai.vue';
import DashboardSettings from './dashboard-settings.vue';

defineOptions({
  name: 'DashboardHeader'
});

interface Props {
  title?: string;
}

interface Emits {
  (e: 'update:scale', value: boolean): void;
}

defineProps<Props>();
const emit = defineEmits<Emits>();

const router = useRouter();
const { isFullscreen, toggle } = useFullscreen();
const aiVisible = ref(false);
const settingsVisible = ref(false);
const scaleEnabled = ref(true);

const menuItems = [
  { key: 'home', label: '首页' },
  { key: 'follow', label: '关注' },
  { key: 'recommend', label: '推荐' },
  { key: 'hot', label: '热门' },
  { key: 'local', label: '本地' }
];

const goTo = (key: string) => {
  router.push({ name: key });
};

/**
 * 打开AI对话框
 */
const openAiWindow = () => {
  aiVisible.value = true;
};

/**
 * 打开设置对话框
 */
const openSettingsWindow = () => {
  settingsVisible.value = true;
};

/**
 * 处理缩放设置变更
 */
const handleScaleChange = (value: boolean) => {
  scaleEnabled.value = value;
  emit('update:scale', value);
};
</script>

<template>
  <div
    class="h-75px w-full flex items-center justify-between bg-[length:100%_100%] bg-[url('@/assets/dashboard/imgs/dashboard-header-bg.png')] bg-center bg-no-repeat px-20px relative overflow-hidden"
  >
    <!-- 动态光效背景 -->
    <div class="absolute inset-0 pointer-events-none">
      <div class="absolute inset-0 bg-gradient-to-r from-transparent via-blue-500/10 to-transparent animate-[header-glow_4s_ease-in-out_infinite]" />
      <div class="absolute top-0 left-0 w-full h-1px bg-gradient-to-r from-transparent via-cyan-400/60 to-transparent animate-[border-glow_3s_ease-in-out_infinite]" />
      <div class="absolute bottom-0 left-0 w-full h-1px bg-gradient-to-r from-transparent via-blue-400/60 to-transparent animate-[border-glow_3s_ease-in-out_infinite]" style="animation-delay: 1.5s" />
    </div>
    <!-- 左侧区域 -->
    <div class="w-[40%] flex items-center">
      <img src="@/assets/dashboard/imgs/dashboard-logo.png" class="mr-8px h-50px" alt="logo" />
      <div class="flex items-center">
        <template v-for="(item, index) in menuItems" :key="item.key">
          <NButton text type="primary" @click="goTo(item.key)">
            <span class="text-xl text-[#b1d8ff] hover:underline">{{ item.label }}</span>
          </NButton>
          <div
            v-if="index !== menuItems.length - 1"
            class="mx-10px h-24px w-1px from-[#5b70aa] to-transparent bg-gradient-to-b"
          />
        </template>
      </div>
    </div>

    <!-- 中间标题 -->
    <div class="text-center">
      <h1 class="text-4xl text-white font-bold leading-46px tracking-6px shadow-[0_6px_20px_rgba(1,48,108,0.62)]">
        {{ title }}
      </h1>
    </div>

    <!-- 右侧功能区 -->
    <div class="w-[40%] flex items-center justify-end space-x-4">
      <NButton text type="primary" class="mx-2 px-16px" @click="openAiWindow">
        <template #icon>
          <img src="@/assets/dashboard/imgs/dashboard-robot.png" class="mr-2 h-50px max-w-100px" />
        </template>
      </NButton>

      <div class="mx-10px h-24px w-1px from-[#5b70aa] to-transparent bg-gradient-to-b" />

      <NButton text type="primary" class="flex flex-col px-16px" @click="openAiWindow">
        <template #icon>
          <NIcon>
            <SvgIcon icon="mdi:calendar-clock" class="text-xl text-[#b1d8ff]" />
          </NIcon>
        </template>
        <span class="text-lg text-[#b1d8ff] hover:underline">调度台</span>
      </NButton>

      <div class="mx-10px h-24px w-1px from-[#5b70aa] to-transparent bg-gradient-to-b" />

      <NButton text type="primary" class="flex flex-col px-16px" @click="openAiWindow">
        <template #icon>
          <NIcon>
            <SvgIcon icon="ion:desktop-outline" class="text-xl text-[#b1d8ff]" />
          </NIcon>
        </template>
        <span class="text-lg text-[#b1d8ff] hover:underline">工作台</span>
      </NButton>

      <div class="mx-10px h-24px w-1px from-[#5b70aa] to-transparent bg-gradient-to-b" />

      <NButton text type="primary" class="pl-10px pr-10px" @click="openSettingsWindow">
        <template #icon>
          <NIcon class="text-3xl text-[#b1d8ff]">
            <SvgIcon icon="mdi:cog" />
          </NIcon>
        </template>
      </NButton>

      <div class="mx-10px h-24px w-1px from-[#5b70aa] to-transparent bg-gradient-to-b" />

      <NButton text type="primary" class="pl-10px pr-10px" @click="toggle">
        <template #icon>
          <NIcon class="text-3xl text-[#b1d8ff]">
            <SvgIcon v-if="isFullscreen" icon="gridicons-fullscreen-exit" />
            <SvgIcon v-else icon="gridicons-fullscreen" />
          </NIcon>
        </template>
      </NButton>
    </div>
  </div>

  <!-- AI对话框 -->
  <DashboardAi v-model:visible="aiVisible" iframe-url="https://www.wenxiaobai.com/chat/200006" />
  <!-- 设置对话框 -->
  <DashboardSettings v-model:visible="settingsVisible" @update:scale="handleScaleChange" />
</template>

<style scoped>
/* 头部动画效果 */
@keyframes header-glow {
  0%, 100% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    transform: translateX(100%);
    opacity: 1;
  }
}

@keyframes border-glow {
  0%, 100% {
    transform: scaleX(0);
    opacity: 0;
  }
  50% {
    transform: scaleX(1);
    opacity: 1;
  }
}

/* Logo动画 */
:deep(img) {
  transition: all 0.3s ease;
}

:deep(img:hover) {
  transform: scale(1.05);
  filter: drop-shadow(0 0 10px rgba(59, 130, 246, 0.5));
}

/* 按钮悬停效果增强 */
:deep(.n-button) {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

:deep(.n-button:hover) {
  transform: translateY(-1px);
  filter: drop-shadow(0 4px 8px rgba(59, 130, 246, 0.3));
}

/* 标题发光效果 */
h1 {
  animation: title-glow 3s ease-in-out infinite;
}

@keyframes title-glow {
  0%, 100% {
    text-shadow: 0 6px 20px rgba(1, 48, 108, 0.62);
  }
  50% {
    text-shadow: 0 6px 20px rgba(1, 48, 108, 0.62), 0 0 30px rgba(59, 130, 246, 0.4);
  }
}
</style>
